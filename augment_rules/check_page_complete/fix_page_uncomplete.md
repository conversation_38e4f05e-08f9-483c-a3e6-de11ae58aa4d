# 页面完整性修复规则

## 前置条件
- 网站已运行在 `http://localhost:3000`，无需重新启动
- 根据给定页面路由执行以下修复步骤

## 核心修复步骤

### 1. 页面定位与Service修复

#### 1.1 定位页面文件
根据路由找到对应的React组件文件
- 示例：`system-settings/system-config/system-settings` → `SystemSettings.jsx`

#### 1.2 修复GET请求参数格式
检查页面对应service文件中的GET请求参数传递方式：
- **修复规则**：参数需要用`{}`包裹成对象形式
- **修复前**：`api.get('/接口路径', params)`
- **修复后**：`api.get('/接口路径', { params })`

#### 1.3 定位控制器文件
**重要**：页面通常调用多个API接口，分布在不同控制器文件中

**查找步骤**：
1. 分析页面中所有API接口路径
2. 在`swagger_spider/my-mock-server/routes`目录中找到对应控制器文件
3. **必须找到所有相关控制器文件**

**接口分布示例**：
- `/system/config/list` → `system_config_controller_controller.js`
- `/system/enum/status` → `system_enum_controller_controller.js`
- `/system/user/info` → `system_user_controller_controller.js`

### 2. API参数匹配修复

#### 2.1 参数匹配检查
比较页面中所有接口（GET/POST/PUT/DELETE等）与控制器定义：

**检查内容**：
- 参数名称、类型是否一致
- 是否有参数缺失
- GET请求是否缺少page和pageSize参数

**调用链追踪**：
```
页面组件 → Redux Slice → Service文件 → 控制器接口
```

**修复原则**：
- 参数名称不匹配 → 使用控制器中定义的参数名称
- 参数类型不匹配 → 使用控制器中定义的参数类型
- 参数缺失 → 补充后端要求的参数
- GET请求缺少分页参数 → 补充page和pageSize参数

#### 2.2 PUT请求参数验证

**触发条件**：页面包含PUT请求

**验证要点**：
- 对比控制器API定义，确认参数通过query还是request body传递
- 检查axios调用的参数配置是否正确

**数组参数处理**：
PUT请求通过query传递数组时，使用重复参数名形式：`ids=1&ids=2&ids=3`

```javascript
// 使用paramsSerializer（推荐）
axios.put('/api/endpoint', null, {
  params: { ids: [1, 2, 3] },
  paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' })
})

// 修复示例
const batchUpdate = (ids) => {
  // 根据swagger定义选择合适方式
  return api.put('/api/batch-update', {
    params: { ids },  // query参数
    paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' })
  });
  // 或者: return api.put('/api/batch-update', { data: { ids } }); // request body
};
```

### 3. 用户交互状态优化

#### 3.1 删除功能加载状态

**触发条件**：页面包含删除功能但缺少加载状态展示

**方案A：Redux状态管理（推荐）**
```jsx
// 获取删除状态
const { deleteData, batchDeleteData } = useSelector((state) => state.yourSlice);
const isDeleting = deleteData.loading || batchDeleteData.loading;

// 表格加载状态
<Table loading={loading || isDeleting} />

// 删除函数
const handleDelete = async (record) => {
  try {
    await dispatch(deleteAction(record.id)).unwrap();
    dispatch(fetchDataList(searchParams));
  } catch (error) {
    message.error("删除失败");
  }
};

// 按钮状态
<Button danger loading={deleteData.loading}>删除</Button>
```

**方案B：本地状态管理（备用）**
```jsx
const [deletingId, setDeletingId] = useState(null);

const handleDelete = async (record) => {
  setDeletingId(record.id);
  try {
    await dispatch(deleteAction(record.id)).unwrap();
    fetchData();
  } finally {
    setDeletingId(null);
  }
};

<Button loading={deletingId === record.id}>删除</Button>
```

#### 3.2 添加/编辑操作加载状态

**触发条件**：页面包含添加/编辑表单但保存按钮缺少加载状态

```jsx
const [loading, setLoading] = useState(false);

const handleFormSubmit = async () => {
  try {
    setLoading(true);
    const values = await form.validateFields();
    await dispatch(saveAction(values)).unwrap();
    message.success("保存成功");
    onSave && onSave();
  } catch (error) {
    message.error("保存失败");
  } finally {
    setLoading(false);
  }
};

<Button type="primary" onClick={handleFormSubmit} loading={loading}>保存</Button>
<Button onClick={handleCancel} disabled={loading}>取消</Button>
```

### 4. 错误处理优化

#### 4.1 日期范围选择器空值处理

**触发条件**：页面使用`RangePicker`组件出现空值访问错误

**问题原因**：用户清空选择器时，值变为`null`而非`[null, null]`

**修复方式**：
```jsx
// 安全访问
const params = {
  startDate: dateRange && dateRange[0] ? dateRange[0].unix() : null,
  endDate: dateRange && dateRange[1] ? dateRange[1].unix() : null,
};

// 变更处理
const handleDateRangeChange = (dates) => {
  setDateRange(dates || [null, null]);
};

// 状态初始化
const [dateRange, setDateRange] = useState([null, null]);
```



### 5. 表单数据持久化

#### 5.1 标签页切换时表单数据保持

**触发条件**：
- 页面使用`usePageTabs` Hook管理多个视图
- 添加/编辑页面填写数据后，切换标签页时数据被清空

**问题原因**：标签页切换时组件重新渲染，表单状态被重置

**修复步骤**：

**1. 修改usePageTabs Hook添加表单数据管理**
```jsx
// 在 src/hooks/usePageTabs.js 中添加
const [tabFormData, setTabFormData] = useState({});

const saveTabFormData = useCallback((tabKey, formData) => {
  setTabFormData(prev => ({ ...prev, [tabKey]: formData }));
}, []);

const getTabFormData = useCallback((tabKey) => {
  return tabFormData[tabKey] || null;
}, [tabFormData]);

const clearTabFormData = useCallback((tabKey) => {
  setTabFormData(prev => {
    const newData = { ...prev };
    delete newData[tabKey];
    return newData;
  });
}, []);

// 返回对象中添加
return {
  // ... 其他返回值
  saveTabFormData,
  getTabFormData,
  clearTabFormData,
};
```

**2. 主页面组件传递管理函数**
```jsx
const {
  activeTab, editingRecord,
  saveTabFormData, getTabFormData, clearTabFormData,
} = usePageTabs({...});

<YourFormComponent
  editingRecord={activeTab.startsWith("add-") ? null : editingRecord}
  tabKey={activeTab}
  saveTabFormData={saveTabFormData}
  getTabFormData={getTabFormData}
  clearTabFormData={clearTabFormData}
/>
```

**3. 表单组件实现数据持久化**
```jsx
function YourFormComponent({
  editingRecord, tabKey, saveTabFormData, getTabFormData, clearTabFormData
}) {
  const [form] = Form.useForm();

  // 智能表单初始化
  useEffect(() => {
    if (editingRecord) {
      form.setFieldsValue(editingRecord);
    } else if (tabKey && getTabFormData) {
      const savedFormData = getTabFormData(tabKey);
      savedFormData ? form.setFieldsValue(savedFormData) : form.resetFields();
    }
  }, [editingRecord, form, tabKey, getTabFormData]);

  // 实时保存表单数据（仅添加模式）
  <Form
    form={form}
    onValuesChange={() => {
      if (!editingRecord && tabKey && saveTabFormData) {
        const formData = form.getFieldsValue();
        const hasData = Object.values(formData).some(
          (value) => value && value.toString().trim() !== ""
        );
        if (hasData) saveTabFormData(tabKey, formData);
      }
    }}
  >
    {/* 表单项 */}
  </Form>

  // 提交成功和取消时清除数据
  const handleFormSubmit = async () => {
    try {
      // ... 提交逻辑
      if (!editingRecord && tabKey && clearTabFormData) {
        clearTabFormData(tabKey);
      }
    } catch (error) {
      // 错误处理
    }
  };
}
```

**功能特点**：
- 数据持久化：标签页切换时表单数据不丢失
- 实时保存：用户输入时自动保存
- 智能清理：提交成功、取消或关闭标签页时清除数据
- 编辑模式兼容：不影响编辑模式正常工作

## 执行原则

### 核心要求
- **按顺序执行**：各修复步骤相对独立但需按序进行
- **参数匹配**：确保前端请求与后端接口定义完全匹配
- **保持结构**：修复时保持代码整体结构和逻辑不变
- **完整追踪**：追踪完整调用链（页面组件 → Redux → Service → 控制器）

### 关键检查点
- [ ] 页面文件定位正确
- [ ] Service文件GET请求参数格式修复
- [ ] 所有相关控制器文件已找到
- [ ] API参数名称、类型、完整性检查
- [ ] PUT请求参数传递方式验证
- [ ] 删除功能加载状态添加
- [ ] 添加/编辑操作加载状态添加
- [ ] 日期范围选择器空值处理
- [ ] 表单数据持久化（如使用usePageTabs）

### 实施标准
- 确保所有用户交互都有适当的加载状态反馈
- 特别注意日期范围选择器的空值处理，防止运行时错误
- 优先使用Redux状态管理实现覆盖式加载效果
- 保持一致的用户体验和实现方式